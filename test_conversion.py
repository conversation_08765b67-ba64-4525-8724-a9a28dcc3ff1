#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试转换功能
"""

import sys
import os
sys.path.append('.')

from csv2sol import read_csv_station_info, read_soil_layers_data

def test_conversion(csv_file):
    """测试转换功能"""
    print(f"测试文件: {csv_file}")
    
    # 测试读取站点信息
    print("\n=== 测试读取站点信息 ===")
    csv_data = read_csv_station_info(csv_file)
    if csv_data:
        print("成功读取站点信息:")
        for key, value in csv_data.items():
            print(f"  {key}: {value}")
        
        # 获取站点编号
        station_id = None
        possible_id_keys = ['站点编号', 'soil_id', '站点ID', 'station_id']
        for key in possible_id_keys:
            if key in csv_data and csv_data[key] is not None:
                station_id = csv_data[key]
                break
        
        print(f"\n站点编号: {station_id}")
        
        if station_id:
            # 测试读取土壤层数据
            print("\n=== 测试读取土壤层数据 ===")
            soil_layers = read_soil_layers_data(csv_file, station_id)
            print(f"读取到 {len(soil_layers)} 层土壤数据")
            
            for i, layer in enumerate(soil_layers):
                print(f"第{i+1}层 (slb={layer.get('slb')}): {layer}")
    else:
        print("读取站点信息失败")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_conversion(sys.argv[1])
    else:
        print("使用方法: python test_conversion.py <csv_file>")
