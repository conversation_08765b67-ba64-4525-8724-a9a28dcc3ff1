#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV到土壤文件转换工具
将CSV文件转换为无后缀的土壤站点文件
"""

import pandas as pd
import json
import os
import sys
from pathlib import Path


def read_csv_station_info(csv_file_path):
    """
    读取CSV文件并提取站点信息

    Args:
        csv_file_path (str): CSV文件路径

    Returns:
        dict: 包含站点编号和其他信息的字典
    """
    try:
        # 尝试不同的编码方式
        encodings = ["utf-8", "gbk", "gb2312", "utf-8-sig"]
        df = None

        for encoding in encodings:
            try:
                # 只读取前3行（站点基本信息部分）
                df = pd.read_csv(csv_file_path, header=None, encoding=encoding, nrows=3)
                print(f"成功使用编码 {encoding} 读取文件")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                if "Expected" in str(e) and "fields" in str(e):
                    # 如果是字段数量不匹配，尝试只读取前3行
                    continue
                else:
                    raise e

        if df is None:
            raise ValueError("无法使用任何编码读取CSV文件")

        # 检查文件格式
        if len(df) < 3:
            raise ValueError("CSV文件格式不正确，至少需要3行数据")

        # 第一行是中文列名，第二行是英文列名，第三行是数据
        chinese_headers = df.iloc[0].tolist()
        english_headers = df.iloc[1].tolist()
        data_row = df.iloc[2].tolist()

        print(f"中文列名: {chinese_headers}")
        print(f"英文列名: {english_headers}")
        print(f"数据行: {data_row}")

        # 创建数据字典
        station_data = {}
        for ch_header, en_header, value in zip(
            chinese_headers, english_headers, data_row
        ):
            station_data[ch_header] = value
            station_data[en_header] = value  # 同时保存英文列名映射

        # 根据可能的列名获取站点编号
        station_id = None
        possible_id_keys = ["站点编号", "soil_id", "站点ID", "station_id"]
        for key in possible_id_keys:
            if key in station_data and station_data[key] is not None:
                station_id = station_data[key]
                break

        print(f"从CSV文件读取到站点编号: {station_id}")
        return station_data

    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return None


def query_soil_station_excel(excel_file_path, station_id):
    """
    在Excel文件中查询站点信息

    Args:
        excel_file_path (str): Excel文件路径
        station_id: 站点编号

    Returns:
        dict: 查询到的站点信息，如果未找到返回None
    """
    try:
        # 根据文件扩展名选择读取方式
        if excel_file_path.endswith(".xlsx") or excel_file_path.endswith(".xls"):
            df = pd.read_excel(excel_file_path)
        else:
            # 假设是CSV文件
            df = pd.read_csv(excel_file_path, encoding="utf-8")

        print(f"土壤站点表文件形状: {df.shape}")
        print(f"列名: {list(df.columns)}")

        # 检查文件是否有足够的列
        if df.shape[1] < 5:
            print(f"错误: 土壤站点表文件列数不足，需要至少5列，实际只有{df.shape[1]}列")
            return None

        # 查找站点编号列
        station_id_str = str(station_id)

        # 尝试不同的可能列位置和列名
        possible_columns = [
            4,  # 第5列（索引4）
            'station_id',  # 列名
            'suid',  # 可能的列名
            '站点编号'  # 中文列名
        ]

        matching_rows = None
        used_column = None

        for col in possible_columns:
            try:
                if isinstance(col, int):
                    # 使用列索引
                    if col < df.shape[1]:
                        matching_rows = df[df.iloc[:, col].astype(str) == station_id_str]
                        used_column = f"第{col+1}列"
                else:
                    # 使用列名
                    if col in df.columns:
                        matching_rows = df[df[col].astype(str) == station_id_str]
                        used_column = f"列'{col}'"

                if matching_rows is not None and not matching_rows.empty:
                    print(f"在{used_column}中找到站点编号 {station_id}")
                    break

            except Exception as e:
                print(f"尝试列 {col} 时出错: {e}")
                continue

        if matching_rows is None or matching_rows.empty:
            print(f"在站点信息文件中未找到站点编号 {station_id} 的信息")
            print(f"已尝试的列: {possible_columns}")
            # 显示前几行数据帮助调试
            print("文件前3行数据:")
            print(df.head(3))
            return None

        # 取第一个匹配的行
        row = matching_rows.iloc[0]
        print(f"在站点信息文件中找到匹配的站点信息")

        return row.to_dict()

    except Exception as e:
        print(f"查询站点信息文件时出错: {e}")
        print(f"错误类型: {type(e).__name__}")
        return None


def create_soil_data_json(csv_data, excel_data):
    """
    创建土壤数据的JSON格式

    Args:
        csv_data (dict): CSV文件中的数据
        excel_data (dict): Excel文件中的数据

    Returns:
        dict: 格式化的土壤数据
    """
    # 基础模板
    soil_data = {
        "country": "WD",
        "province": "null",
        "city": "null",
        "county": "null",
        "suid": "null",
        "site": "null",
        "lat": "null",
        "lon": "null",
        "altitude": "null",
        "tcid": 1,
        "scid": 1,
        "prid": "WD-Hh",
        "prop": "60",
        "claf": "Hh",
        "author": "null",
        "source": "null",
        "comments": "null",
        "measure_date": "null",
        "sadat": "null",
        "scsfam": "null",
        "scom": "null",
        "salb": "null",
        "slu1": "null",
        "sldr": "null",
        "drain": "W",
        "slro": "null",
        "slnf": "null",
        "slrf": "null",
        "slpf": "null",
        "smhb": "null",
        "smpx": "null",
        "smke": "null",
        "distance": "null",
    }

    # 从CSV数据中获取经纬度
    if csv_data:
        if "经度" in csv_data and csv_data["经度"] is not None:
            try:
                soil_data["lon"] = float(csv_data["经度"])
            except (ValueError, TypeError):
                pass

        if "纬度" in csv_data and csv_data["纬度"] is not None:
            try:
                soil_data["lat"] = float(csv_data["纬度"])
            except (ValueError, TypeError):
                pass

    # 从Excel数据中填充其他字段（根据实际Excel文件结构调整）
    if excel_data:
        # 根据示例Excel文件的列名进行映射
        column_mapping = {
            "country": "country",
            "province": "null",
            "city": "null",
            "county": "null",
            "station_id": "suid",
            "site": "null",
            "lat": "lat",
            "lon": "lon",
            "altitude": "null",
            "tcid": "tcid",
            "scid": "scid",
            "prid": "prid",
            "prop": "prop",
            "claf": "claf",
            "author": "author",
            "source": "source",
            "comments": "comments",
            "measure_date": "measure_date",
            "sadat": "sadat",
            "scsfam": "scsfam",
            "scom": "scom",
            "salb": "salb",
            "slu1": "slu1",
            "sldr": "sldr",
            "drain": "drain",
            "slro": "slro",
            "slnf": "slnf",
            "slrf": "slrf",
            "slpf": "slpf",
            "smhb": "smhb",
            "smpx": "smpx",
            "smke": "smke",
            "distance": "distance",
        }

        for excel_col, json_field in column_mapping.items():
            if excel_col in excel_data and excel_data[excel_col] is not None:
                # 特殊处理数值类型字段
                if json_field in ["lat", "lon", "altitude", "tcid", "scid"]:
                    try:
                        if json_field in ["tcid", "scid"]:
                            soil_data[json_field] = int(excel_data[excel_col])
                        else:
                            soil_data[json_field] = float(excel_data[excel_col])
                    except (ValueError, TypeError):
                        soil_data[json_field] = excel_data[excel_col]
                else:
                    soil_data[json_field] = excel_data[excel_col]

    return soil_data


def read_soil_layers_data(csv_file_path, station_id):
    """
    读取CSV文件中的土壤层数据（从第8行开始）

    Args:
        csv_file_path (str): CSV文件路径
        station_id: 站点编号

    Returns:
        list: 土壤层数据列表
    """
    try:
        # 尝试不同的编码方式
        encodings = ["utf-8", "gbk", "gb2312", "utf-8-sig", "cp936"]
        data_lines = []
        column_names = []

        for encoding in encodings:
            try:
                # 逐行读取文件来处理不同列数的问题
                with open(csv_file_path, "r", encoding=encoding) as f:
                    lines = f.readlines()

                print(f"成功使用编码 {encoding} 读取文件，共 {len(lines)} 行")

                # 找到土壤层数据的开始位置
                if len(lines) >= 8:
                    # 第7行（索引6）是英文列名
                    column_line = lines[6].strip()
                    column_names = [col.strip() for col in column_line.split(",")]

                    # 第8行开始（索引7开始）是数据
                    data_lines = []
                    for i in range(7, len(lines)):
                        line = lines[i].strip()
                        if line:  # 跳过空行
                            data_lines.append(line)

                    print(f"提取到土壤层数据: {len(data_lines)} 行")
                    break
                else:
                    print(f"文件行数不足: {len(lines)}")
                    continue
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取失败: {e}")
                continue

        if not data_lines:
            print("没有找到土壤层数据")
            return []

        print(f"土壤层数据列名: {column_names}")

        # 处理数据行
        soil_layers = []
        for line in data_lines:
            row_data = [val.strip() for val in line.split(",")]

            # 检查是否为空行
            if all(val == "" for val in row_data):
                continue

            # 创建土壤层数据字典
            layer_data = {
                "suid": station_id,
                "scid": 1,
                "slb": "null",
                "slmh": "null",
                "slll": "null",
                "sdul": "null",
                "ssat": "null",
                "srgf": "1",
                "ssks": "null",
                "sabd": "null",
                "sbdm": "null",
                "slcl": "null",
                "slsi": "null",
                "slsd": "null",
                "slcf": "null",
                "sloc": "null",
                "saoc": "null",
                "sadc": "null",
                "cnrt": "null",
                "sani": "null",
                "slni": "null",
                "snh4": "null",
                "sno3": "null",
                "sapx": "null",
                "slpx": "null",
                "slpt": "null",
                "slpo": "null",
                "slpa": "null",
                "slpb": "null",
                "sake": "null",
                "slke": "null",
                "sahw": "null",
                "slhw": "null",
                "sahb": "null",
                "slhb": "null",
                "scec": "null",
                "ecec": "null",
                "cecc": "null",
                "caco3": "null",
                "slca": "null",
                "slal": "null",
                "slfe": "null",
                "slmn": "null",
                "slmg": "null",
                "slna": "null",
                "slsu": "null",
                "slbs": "null",
                "slec": "null",
                "sltx": "null",
                "sl20": "null",
            }

            # 根据CSV列名映射数据
            csv_to_json_mapping = {
                "soil_id": "suid",
                "dept": "slb",
                "BD": "sabd",
                "LL": "slll",
                "DUL": "sdul",
                "SAT": "ssat",
                "FC": "slcf",  # 暂时不映射
                "ph": "sl20",
                "SW": "null",  # 暂时不映射
                "no3_n": "sno3",
                "nh4_n": "snh4",
                "total_n": "slni",
                "organic_c": "sloc",
            }

            # 填充数据
            for col_name, value in zip(column_names, row_data):
                if not value or value == "":
                    continue

                json_field = csv_to_json_mapping.get(col_name)
                if json_field and json_field in layer_data:
                    # 跳过suid，因为我们使用传入的station_id
                    if json_field == "suid":
                        continue

                    # 转换数据类型
                    if json_field in ["scid", "slb"]:
                        try:
                            layer_data[json_field] = int(float(value))
                        except (ValueError, TypeError):
                            layer_data[json_field] = value
                    elif json_field in [
                        "slll",
                        "sdul",
                        "ssat",
                        "sabd",
                        "slhw",
                        "sloc",
                        "slni",
                    ]:
                        try:
                            layer_data[json_field] = str(float(value))
                        except (ValueError, TypeError):
                            layer_data[json_field] = str(value)
                    else:
                        layer_data[json_field] = str(value)

            # 设置一些默认值
            layer_data["sbdm"] = layer_data["sabd"]  # sbdm = sabd
            layer_data["saoc"] = layer_data["sloc"]  # saoc = sloc
            layer_data["sadc"] = layer_data["sloc"]  # sadc = sloc
            layer_data["sahb"] = layer_data["slhw"]  # sahb = slhw
            layer_data["sani"] = layer_data["slni"]  # sani = slni

            soil_layers.append(layer_data)

        print(f"读取到 {len(soil_layers)} 层土壤数据")
        return soil_layers

    except Exception as e:
        print(f"读取土壤层数据时出错: {e}")
        return []


def save_soil_file(station_id, soil_data, soil_layers, output_dir="."):
    """
    保存土壤数据到无后缀文件

    Args:
        station_id: 站点编号（用作文件名）
        soil_data (dict): 土壤基本数据
        soil_layers (list): 土壤层数据列表
        output_dir (str): 输出目录
    """
    try:
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        # 文件路径（无后缀）
        file_path = os.path.join(output_dir, str(station_id))

        # 保存为单行格式（不换行）
        with open(file_path, "w", encoding="utf-8") as f:
            # 第一行：基本信息（不换行）
            json.dump(soil_data, f, ensure_ascii=False, separators=(",", ":"))
            f.write("\n")  # 换行
            # 第二行：土壤层数据（不换行）
            json.dump(soil_layers, f, ensure_ascii=False, separators=(",", ":"))

        print(f"成功保存文件: {file_path}")
        return True

    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False


def process_single_csv(csv_file_path, excel_file_path, output_dir):
    """
    处理单个CSV文件

    Args:
        csv_file_path (str): CSV文件路径
        excel_file_path (str): Excel文件路径
        output_dir (str): 输出目录

    Returns:
        bool: 是否成功处理
    """
    print(f"开始处理CSV文件: {os.path.basename(csv_file_path)}")

    # 读取CSV文件
    csv_data = read_csv_station_info(csv_file_path)
    if not csv_data:
        print("无法读取CSV文件数据")
        return False

    # 获取站点编号
    station_id = None
    possible_id_keys = ["站点编号", "soil_id", "站点ID", "station_id"]
    for key in possible_id_keys:
        if key in csv_data and csv_data[key] is not None:
            station_id = csv_data[key]
            break

    if not station_id:
        print("错误: 无法从CSV文件中获取站点编号")
        print(f"可用的键: {list(csv_data.keys())}")
        return False

    print(f"处理站点编号: {station_id}")

    # 查询Excel文件
    excel_data = None
    if excel_file_path:
        excel_data = query_soil_station_excel(excel_file_path, station_id)

        # 如果在土壤站点表中没有找到对应信息，跳过转换
        if excel_data is None:
            print(f"跳过转换: 在土壤站点表中未找到站点编号 {station_id} 的信息")
            return False
    else:
        print("警告: 没有提供土壤站点表文件，跳过转换")
        return False

    # 创建土壤基本数据
    soil_data = create_soil_data_json(csv_data, excel_data)

    # 读取土壤层数据
    print("开始读取土壤层数据...")
    soil_layers = read_soil_layers_data(csv_file_path, station_id)

    if not soil_layers:
        print("警告: 没有读取到土壤层数据")

    # 保存文件
    if save_soil_file(station_id, soil_data, soil_layers, output_dir):
        print(f"转换完成! 输出文件: {os.path.join(output_dir, str(station_id))}")
        print(f"包含 {len(soil_layers)} 层土壤数据")
        return True
    else:
        print("转换失败!")
        return False


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) < 2:
        print(
            "使用方法: python csv2sol.py <csv_folder_path> [excel_file_path] [output_dir]"
        )
        print("示例: python csv2sol.py ./csv_files 新土壤站点表.csv ./output")
        print("说明:")
        print("  csv_folder_path: 包含CSV文件的文件夹路径")
        print("  excel_file_path: 土壤站点表CSV文件路径（默认: 新土壤站点表.csv）")
        print("  output_dir: 输出目录（默认: 当前目录）")
        return

    csv_folder_path = sys.argv[1]
    excel_file_path = sys.argv[2] if len(sys.argv) > 2 else "新土壤站点表.csv"
    output_dir = sys.argv[3] if len(sys.argv) > 3 else "."

    # 检查输入路径
    if not os.path.exists(csv_folder_path):
        print(f"错误: 路径不存在: {csv_folder_path}")
        return

    # 如果输入是文件而不是文件夹，按单文件处理
    if os.path.isfile(csv_folder_path):
        print("检测到输入是单个文件，按单文件模式处理")
        if not os.path.exists(excel_file_path):
            print(f"警告: Excel文件不存在: {excel_file_path}")
            excel_file_path = None

        success = process_single_csv(csv_folder_path, excel_file_path, output_dir)
        if success:
            print("\n单文件处理完成!")
        else:
            print("\n单文件处理失败!")
        return

    # 检查是否是文件夹
    if not os.path.isdir(csv_folder_path):
        print(f"错误: {csv_folder_path} 不是有效的文件夹")
        return

    # 检查Excel文件
    if not os.path.exists(excel_file_path):
        print(f"错误: 土壤站点表文件不存在: {excel_file_path}")
        return

    # 查找文件夹中的所有CSV文件
    csv_files = []
    for file_name in os.listdir(csv_folder_path):
        if file_name.lower().endswith(".csv"):
            csv_files.append(os.path.join(csv_folder_path, file_name))

    if not csv_files:
        print(f"错误: 在文件夹 {csv_folder_path} 中没有找到CSV文件")
        return

    print(f"找到 {len(csv_files)} 个CSV文件:")
    for csv_file in csv_files:
        print(f"  - {os.path.basename(csv_file)}")

    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # 处理统计
    total_files = len(csv_files)
    success_count = 0
    failed_count = 0
    skipped_count = 0

    print(f"\n开始批量处理 {total_files} 个CSV文件...")
    print("=" * 60)

    # 逐个处理CSV文件
    for i, csv_file in enumerate(csv_files, 1):
        print(f"\n[{i}/{total_files}] 处理文件: {os.path.basename(csv_file)}")

        try:
            success = process_single_csv(csv_file, excel_file_path, output_dir)
            if success:
                success_count += 1
                print(f"✅ 成功")
            else:
                skipped_count += 1
                print(f"⏭️ 跳过")
        except Exception as e:
            failed_count += 1
            print(f"❌ 处理失败: {e}")

    # 输出处理结果统计
    print("\n" + "=" * 60)
    print("批量处理完成!")
    print(f"总文件数: {total_files}")
    print(f"成功转换: {success_count}")
    print(f"跳过文件: {skipped_count} (站点未找到)")
    print(f"失败文件: {failed_count}")
    print(f"输出目录: {output_dir}")

    if success_count > 0:
        print(f"\n✅ 成功转换了 {success_count} 个文件!")
    if skipped_count > 0:
        print(f"⏭️ 跳过了 {skipped_count} 个文件 (在土壤站点表中未找到对应站点)")
    if failed_count > 0:
        print(f"❌ {failed_count} 个文件处理失败")


if __name__ == "__main__":
    main()
