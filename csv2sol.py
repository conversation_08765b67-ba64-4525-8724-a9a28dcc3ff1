#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV到土壤文件转换工具
将CSV文件转换为无后缀的土壤站点文件
"""

import pandas as pd
import json
import os
import sys
from pathlib import Path


def read_csv_station_info(csv_file_path):
    """
    读取CSV文件并提取站点信息

    Args:
        csv_file_path (str): CSV文件路径

    Returns:
        dict: 包含站点编号和其他信息的字典
    """
    try:
        # 读取CSV文件，不使用第一行作为header
        df = pd.read_csv(csv_file_path, header=None, encoding='utf-8')

        # 检查文件格式
        if len(df) < 3:
            raise ValueError("CSV文件格式不正确，至少需要3行数据")

        # 第一行是中文列名，第二行是英文列名，第三行是数据
        chinese_headers = df.iloc[0].tolist()
        # english_headers = df.iloc[1].tolist()  # 暂时不需要使用
        data_row = df.iloc[2].tolist()

        # 创建数据字典
        station_data = {}
        for ch_header, value in zip(chinese_headers, data_row):
            station_data[ch_header] = value

        print(f"从CSV文件读取到站点编号: {station_data.get('站点编号', 'N/A')}")
        return station_data

    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return None


def query_soil_station_excel(excel_file_path, station_id):
    """
    在Excel文件中查询站点信息

    Args:
        excel_file_path (str): Excel文件路径
        station_id: 站点编号

    Returns:
        dict: 查询到的站点信息，如果未找到返回None
    """
    try:
        # 根据文件扩展名选择读取方式
        if excel_file_path.endswith('.xlsx') or excel_file_path.endswith('.xls'):
            df = pd.read_excel(excel_file_path)
        else:
            # 假设是CSV文件
            df = pd.read_csv(excel_file_path, encoding='utf-8')

        # 假设第五列是站点编号列（索引为4）
        # 查找匹配的行
        matching_rows = df[df.iloc[:, 4] == station_id]

        if matching_rows.empty:
            print(f"在站点信息文件中未找到站点编号 {station_id} 的信息")
            return None

        # 取第一个匹配的行
        row = matching_rows.iloc[0]
        print(f"在站点信息文件中找到匹配的站点信息")

        return row.to_dict()

    except Exception as e:
        print(f"查询站点信息文件时出错: {e}")
        return None


def create_soil_data_json(csv_data, excel_data):
    """
    创建土壤数据的JSON格式

    Args:
        csv_data (dict): CSV文件中的数据
        excel_data (dict): Excel文件中的数据

    Returns:
        dict: 格式化的土壤数据
    """
    # 基础模板
    soil_data = {
        "country": "WD",
        "province": None,
        "city": None,
        "county": None,
        "suid": None,
        "site": None,
        "lat": None,
        "lon": None,
        "altitude": None,
        "tcid": 1,
        "scid": 1,
        "prid": "WD-Hh",
        "prop": "60",
        "claf": "Hh",
        "author": None,
        "source": None,
        "comments": None,
        "measure_date": None,
        "sadat": None,
        "scsfam": None,
        "scom": None,
        "salb": None,
        "slu1": None,
        "sldr": None,
        "drain": "W",
        "slro": None,
        "slnf": None,
        "slrf": None,
        "slpf": None,
        "smhb": None,
        "smpx": None,
        "smke": None,
        "distance": None
    }

    # 从CSV数据中获取经纬度
    if csv_data:
        if '经度' in csv_data and csv_data['经度'] is not None:
            try:
                soil_data['lon'] = float(csv_data['经度'])
            except (ValueError, TypeError):
                pass

        if '纬度' in csv_data and csv_data['纬度'] is not None:
            try:
                soil_data['lat'] = float(csv_data['纬度'])
            except (ValueError, TypeError):
                pass

    # 从Excel数据中填充其他字段（根据实际Excel文件结构调整）
    if excel_data:
        # 根据示例Excel文件的列名进行映射
        column_mapping = {
            'country': 'country',
            'province': 'province',
            'city': 'city',
            'county': 'county',
            'station_id': 'suid',
            'site': 'site',
            'lat': 'lat',
            'lon': 'lon',
            'altitude': 'altitude',
            'tcid': 'tcid',
            'scid': 'scid',
            'prid': 'prid',
            'prop': 'prop',
            'claf': 'claf',
            'author': 'author',
            'source': 'source',
            'comments': 'comments',
            'measure_date': 'measure_date',
            'sadat': 'sadat',
            'scsfam': 'scsfam',
            'scom': 'scom',
            'salb': 'salb',
            'slu1': 'slu1',
            'sldr': 'sldr',
            'drain': 'drain',
            'slro': 'slro',
            'slnf': 'slnf',
            'slrf': 'slrf',
            'slpf': 'slpf',
            'smhb': 'smhb',
            'smpx': 'smpx',
            'smke': 'smke',
            'distance': 'distance'
        }

        for excel_col, json_field in column_mapping.items():
            if excel_col in excel_data and excel_data[excel_col] is not None:
                # 特殊处理数值类型字段
                if json_field in ['lat', 'lon', 'altitude', 'tcid', 'scid']:
                    try:
                        if json_field in ['tcid', 'scid']:
                            soil_data[json_field] = int(excel_data[excel_col])
                        else:
                            soil_data[json_field] = float(excel_data[excel_col])
                    except (ValueError, TypeError):
                        soil_data[json_field] = excel_data[excel_col]
                else:
                    soil_data[json_field] = excel_data[excel_col]

    return soil_data


def save_soil_file(station_id, soil_data, output_dir="."):
    """
    保存土壤数据到无后缀文件

    Args:
        station_id: 站点编号（用作文件名）
        soil_data (dict): 土壤数据
        output_dir (str): 输出目录
    """
    try:
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        # 文件路径（无后缀）
        file_path = os.path.join(output_dir, str(station_id))

        # 保存为JSON格式
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(soil_data, f, ensure_ascii=False, indent=2)

        print(f"成功保存文件: {file_path}")
        return True

    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法: python csv2sol.py <csv_file_path> [excel_file_path] [output_dir]")
        print("示例: python csv2sol.py station_data.csv 土壤站点表soil_station_info.xlsx ./output")
        return

    csv_file_path = sys.argv[1]
    excel_file_path = sys.argv[2] if len(sys.argv) > 2 else "土壤站点表soil_station_info.csv"
    output_dir = sys.argv[3] if len(sys.argv) > 3 else "."

    # 检查文件是否存在
    if not os.path.exists(csv_file_path):
        print(f"错误: CSV文件不存在: {csv_file_path}")
        return

    if not os.path.exists(excel_file_path):
        print(f"警告: Excel文件不存在: {excel_file_path}")
        excel_file_path = None

    print(f"开始处理CSV文件: {csv_file_path}")

    # 读取CSV文件
    csv_data = read_csv_station_info(csv_file_path)
    if not csv_data:
        print("无法读取CSV文件数据")
        return

    # 获取站点编号
    station_id = csv_data.get('站点编号')
    if not station_id:
        print("错误: 无法从CSV文件中获取站点编号")
        return

    print(f"处理站点编号: {station_id}")

    # 查询Excel文件
    excel_data = None
    if excel_file_path:
        excel_data = query_soil_station_excel(excel_file_path, station_id)

    # 创建土壤数据
    soil_data = create_soil_data_json(csv_data, excel_data)

    # 保存文件
    if save_soil_file(station_id, soil_data, output_dir):
        print(f"转换完成! 输出文件: {os.path.join(output_dir, str(station_id))}")
    else:
        print("转换失败!")


if __name__ == "__main__":
    main()