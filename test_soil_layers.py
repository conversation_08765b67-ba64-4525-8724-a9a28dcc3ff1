#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试土壤层数据读取
"""

import pandas as pd

def test_soil_layers_reading(file_path):
    """测试读取土壤层数据"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp936']
    
    print("=== 测试读取完整文件 ===")
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, header=None, encoding=encoding)
            print(f"编码 {encoding}: 成功读取 {df.shape[0]} 行 {df.shape[1]} 列")
            print("前10行内容:")
            for i in range(min(10, len(df))):
                print(f"第{i+1}行: {df.iloc[i].tolist()}")
            print()
            break
        except Exception as e:
            print(f"编码 {encoding}: 失败 - {e}")
    
    print("\n=== 测试读取土壤层数据（从第8行开始） ===")
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, header=None, encoding=encoding, skiprows=7)
            print(f"编码 {encoding}: 成功读取土壤层数据 {df.shape[0]} 行 {df.shape[1]} 列")
            if len(df) > 0:
                print("列名（第8行）:", df.iloc[0].tolist())
            if len(df) > 1:
                print("数据行:")
                for i in range(1, min(6, len(df))):
                    print(f"  第{i}层: {df.iloc[i].tolist()}")
            break
        except Exception as e:
            print(f"编码 {encoding}: 失败 - {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        test_soil_layers_reading(file_path)
    else:
        print("使用方法: python test_soil_layers.py <csv_file>")
