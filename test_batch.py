#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量处理功能
"""

import subprocess
import sys
import os

def test_batch_processing():
    """测试批量处理"""
    print("测试批量处理功能...")
    
    # 检查文件夹是否存在
    if not os.path.exists("csv_test_folder"):
        print("错误: csv_test_folder 不存在")
        return
    
    if not os.path.exists("土壤站点表soil_station_info.csv"):
        print("错误: 土壤站点表soil_station_info.csv 不存在")
        return
    
    # 运行批量处理
    cmd = [sys.executable, "csv2sol.py", "csv_test_folder", "土壤站点表soil_station_info.csv", "output"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        print("命令输出:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except Exception as e:
        print(f"运行失败: {e}")

if __name__ == "__main__":
    test_batch_processing()
